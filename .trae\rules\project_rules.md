# AI Development Rules

This document outlines the technology stack, MCP usage, and specific library guidelines for the Next.js application.
Adhering to these rules will help maintain consistency, improve collaboration, and ensure the AI assistant can effectively understand and modify the codebase. please use Indonesian language for text in UI. dont translate text on base ui in components/ui. also, please use Indonesian language for text in error message.
use bprogress/next as top progress bar so i know there is progress at the background, such as page transition and any loading.

---

## 📌 Quick Cheat Sheet

| Category         | Rule                                                                                                                                                                                                                        |
| ---------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| \[byterover-mcp] | - Use `byterover-retrive-knowledge` **before tasks**<br>- Use `byterover-store-knowledge` **after tasks**                                                                                                                   |
| \[context7]      | Always use `context7` before tasks                                                                                                                                                                                          |
| \[directus]      | - Use **Directus SDK** for all CRUD<br>- SDK initialized in `lib/directus/client.ts`<br>- Auth logic in `lib/directus/auth.ts`<br>- Data fetching functions go in `services/`<br>- Use **TanStack Query hooks** in `hooks/` |
| \[shadcn]        | Always use **Shadcn/UI components**                                                                                                                                                                                         |
| UI Components    | Use from `components/ui/` (Shadcn/UI). Custom → follow Shadcn style                                                                                                                                                         |
| Styling          | Tailwind CSS only. No CSS-in-JS                                                                                                                                                                                             |
| Icons            | lucide-react                                                                                                                                                                                                                |
| Forms            | react-hook-form + zod                                                                                                                                                                                                       |
| State Mgmt       | Local: `useState`, `useReducer`<br>Global: `zustand`                                                                                                                                                                        |
| API/Data         | - Directus SDK in `lib/directus/*`<br>- Fetching logic in `services/`<br>- TanStack Query hooks in `hooks/`<br>- Use `app/api/` **only if proxying/transform/security is required**                                         |
| Animations       | `motion`, `tailwindcss-animate`, Radix animations                                                                                                                                                                           |
| Notifications    | Sonner (`components/ui/sonner.tsx`)                                                                                                                                                                                         |
| Charts           | Recharts (`components/ui/chart.tsx`)                                                                                                                                                                                        |
| Utils            | `lib/utils.ts`                                                                                                                                                                                                              |
| Hooks            | `hooks/` directory                                                                                                                                                                                                          |
| TypeScript       | Always strong typing, avoid `any`                                                                                                                                                                                           |

---

## MCP Usage Guidelines

### \[byterover-mcp]

* **Important:**

  * Always use `byterover-retrive-knowledge` to get the related context **before any tasks**.
  * Always use `byterover-store-knowledge` to store all **critical information** after successful tasks.

### \[context7]

* Always use `context7` to get the related context **before any tasks**.

### \[directus]

* For **create, update, delete, read** operations in Directus:

  * Always use the **Directus SDK**.
  * SDK initialized in `lib/directus/client.ts`.
  * Authentication logic in `lib/directus/auth.ts`.
  * Business/domain-specific fetching functions live in `services/`.
  * TanStack Query hooks (`useX`) live in `hooks/`.
  * **Use `app/api/` only when you need proxying, security, or data transformation.**
  * **Use lib/types.ts for type that use data for and from directus.**


### \[shadcn]

* Always use **Shadcn/UI** components.

---

## Tech Stack Overview

The application is built using the following core technologies:

* **Framework:** Next.js (App Router)
* **Language:** TypeScript
* **UI Components:** Shadcn/UI (Radix UI + Tailwind CSS)
* **Styling:** Tailwind CSS (utility-first)
* **Icons:** Lucide React (SVG icons)
* **Forms:** React Hook Form + Zod (schema validation)
* **State Management:** zustand
* **Data Fetching:** Directus SDK + TanStack Query
* **Notifications/Toasts:** Sonner
* **Charts:** Recharts
* **Animations:** `motion`, `tailwindcss-animate`, Radix UI built-in animations

---

## Folder Structure Guidelines

```
src/
├── app/                 # Next.js App Router
│   └── api/             # (Optional) proxy/transform endpoints
│
├── lib/                 
│   ├── directus/
│   │   ├── client.ts    # Directus SDK client
│   │   └── auth.ts      # Auth handling (login, logout, refresh)
│   ├── queryClient.ts   # TanStack Query client
│   └── utils.ts         # Reusable helpers
│
├── services/            # Data layer (Directus CRUD functions)
│   ├── event.service.ts
│   ├── guest.service.ts
│   └── ...
│
├── hooks/               # TanStack Query hooks
│   ├── useEvents.ts
│   ├── useEvent.ts
│   └── ...
│
├── store/               # Zustand stores
│   ├── useAuthStore.ts
│   └── useUIStore.ts
│
├── components/          # UI Components
│   ├── ui/              # Shadcn components
│   └── ...
│
└── types/               # TypeScript types
```

---

## Custom Hooks Guidlines

as long as ahooks provide hooks that needed (such as debounce), use hooks from ahooks.
otherwise, create custom hooks in `hooks/` directory.
custom hooks should be named with `useX` prefix.

---
## Error Handling in Directus SDK

Use the `isDirectusError` type guard when handling errors:

```ts
import { createDirectus, rest, isDirectusError, readItems } from '@directus/sdk';
import { directus } from '@/lib/directus/client';

try {
  const request = await directus.request(readItems('posts'));
} catch (error) {
  if (isDirectusError(error)) {
    // Error returned from Directus API
  } else {
    // Non-API error (e.g., network or JSON parse issue)
  }
}
```

---

# 🔹 Data Flow Diagram

```mermaid
flowchart TD
    subgraph Directus Backend
        D[(Directus API/SDK)]
    end

    subgraph Lib
        C1[lib/directus/client.ts]
        C2[lib/directus/auth.ts]
    end

    subgraph Services
        S1[event.service.ts]
        S2[guest.service.ts]
    end

    subgraph Hooks
        H1[useEvents.ts]
        H2[useGuests.ts]
    end

    subgraph Store (Zustand)
        Z1[useAuthStore.ts]
        Z2[useUIStore.ts]
    end

    subgraph Components
        UI1[EventsPage.tsx]
        UI2[GuestList.tsx]
    end

    %% Flow connections
    D --> C1
    C1 --> C2
    C2 --> S1
    C2 --> S2
    S1 --> H1
    S2 --> H2
    H1 --> UI1
    H2 --> UI2
    C2 --> Z1
    Z1 --> C2
    Z2 --> UI1
    Z2 --> UI2
```

---
