import client from "@/lib/directus";
import { login } from "@directus/sdk";

export async function loginuser(email: string, password: string) {
  await client.request(login({ email, password }));
}

export async function logout() {
  await client.auth.logout();
}

export async function getMe() {
  return await client.users.me.read();
}

export async function refresh() {
  return await client.auth.refresh();
}
