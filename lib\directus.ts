import {
  createDirectus,
  authentication,
  rest,
  AuthenticationClient,
  RestClient,
} from "@directus/sdk";
import { CustomDirectusTypes } from "./types";

type DirectusClientType = ReturnType<
  typeof createDirectus<CustomDirectusTypes>
> &
  AuthenticationClient<CustomDirectusTypes> &
  RestClient<CustomDirectusTypes>;

const client = createDirectus<DirectusClientType>(
  process.env.NEXT_PUBLIC_DIRECTUS_URL || "http://localhost:8055"
)
  .with(authentication("cookie", { credentials: "include", autoRefresh: true }))
  .with(
    rest({
      credentials: "include",
      onRequest: (options) => ({ ...options, cache: "no-store" }),
    })
  );

export default client;
